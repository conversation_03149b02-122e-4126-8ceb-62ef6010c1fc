{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { BrowserRouter as Router, Routes, Route, useLocation } from \"react-router-dom\";\nimport { useEffect, useState } from \"react\";\nimport './css/global/App.css';\nimport Navbar from \"./components/Navbar\";\nimport Loader from \"./components/Loader\";\nimport Home from \"./pages/Home\";\nimport About from \"./pages/About\";\nimport Contact from \"./pages/Contact\";\nimport Login from \"./pages/Login\";\nimport Signup from \"./pages/Signup\";\nimport EmployeeDashboard from \"./pages/EmployeeDashboard\";\nimport AdminDashboard from \"./pages/AdminDashboard\";\nimport FinanceDashboard from \"./pages/FinanceDashboard\";\nimport { AuthProvider } from \"./contexts/AuthContext\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AppContent() {\n  _s();\n  const location = useLocation();\n  const [loading, setLoading] = useState(false);\n  useEffect(() => {\n    setLoading(true);\n    const timer = setTimeout(() => {\n      setLoading(false);\n    }, 1000); // half a second delay\n\n    return () => clearTimeout(timer);\n  }, [location]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/about\",\n        element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/contact\",\n        element: /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/signup\",\n        element: /*#__PURE__*/_jsxDEV(Signup, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 40\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/dashboard/employee\",\n        element: /*#__PURE__*/_jsxDEV(EmployeeDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 52\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/dashboard/admin\",\n        element: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/dashboard/finance\",\n        element: /*#__PURE__*/_jsxDEV(FinanceDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 51\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(AppContent, \"12V4DhK/+FphMBmcfsoKCCzma6g=\", false, function () {\n  return [useLocation];\n});\n_c = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "useLocation", "useEffect", "useState", "<PERSON><PERSON><PERSON>", "Loader", "Home", "About", "Contact", "<PERSON><PERSON>", "Signup", "EmployeeDashboard", "AdminDashboard", "FinanceDashboard", "<PERSON>th<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "A<PERSON><PERSON><PERSON>nt", "_s", "location", "loading", "setLoading", "timer", "setTimeout", "clearTimeout", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "path", "element", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/frontend/src/App.js"], "sourcesContent": ["import { BrowserRouter as Router, Routes, Route, useLocation } from \"react-router-dom\";\nimport { useEffect, useState } from \"react\";\nimport './css/global/App.css';\nimport Navbar from \"./components/Navbar\";\nimport Loader from \"./components/Loader\";\nimport Home from \"./pages/Home\";\nimport About from \"./pages/About\";\nimport Contact from \"./pages/Contact\";\nimport Login from \"./pages/Login\";\nimport Signup from \"./pages/Signup\";\nimport EmployeeDashboard from \"./pages/EmployeeDashboard\";\nimport AdminDashboard from \"./pages/AdminDashboard\";\nimport FinanceDashboard from \"./pages/FinanceDashboard\";\nimport { AuthProvider } from \"./contexts/AuthContext\";\n\nfunction AppContent() {\n  const location = useLocation();\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    setLoading(true);\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n    }, 1000); // half a second delay\n\n    return () => clearTimeout(timer);\n  }, [location]);\n\n  if (loading) {\n    return <Loader />;\n  }\n  return (\n    <>\n      <Navbar />\n      <Routes>\n        <Route path=\"/\" element={<Home />} />\n        <Route path=\"/about\" element={<About />} />\n        <Route path=\"/contact\" element={<Contact />} />\n        <Route path=\"/login\" element={<Login />} />\n        <Route path=\"/signup\" element={<Signup />} />\n        <Route path=\"/dashboard/employee\" element={<EmployeeDashboard />} />\n        <Route path=\"/dashboard/admin\" element={<AdminDashboard />} />\n        <Route path=\"/dashboard/finance\" element={<FinanceDashboard />} />\n      </Routes>\n    </>\n  );\n}\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <AppContent />\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,WAAW,QAAQ,kBAAkB;AACtF,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAO,sBAAsB;AAC7B,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,SAASC,YAAY,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAE7CD,SAAS,CAAC,MAAM;IACdqB,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMG,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,CAACH,QAAQ,CAAC,CAAC;EAEd,IAAIC,OAAO,EAAE;IACX,oBAAON,OAAA,CAACX,MAAM;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;EACA,oBACEd,OAAA,CAAAE,SAAA;IAAAa,QAAA,gBACEf,OAAA,CAACZ,MAAM;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVd,OAAA,CAACjB,MAAM;MAAAgC,QAAA,gBACLf,OAAA,CAAChB,KAAK;QAACgC,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEjB,OAAA,CAACV,IAAI;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrCd,OAAA,CAAChB,KAAK;QAACgC,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEjB,OAAA,CAACT,KAAK;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3Cd,OAAA,CAAChB,KAAK;QAACgC,IAAI,EAAC,UAAU;QAACC,OAAO,eAAEjB,OAAA,CAACR,OAAO;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/Cd,OAAA,CAAChB,KAAK;QAACgC,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEjB,OAAA,CAACP,KAAK;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3Cd,OAAA,CAAChB,KAAK;QAACgC,IAAI,EAAC,SAAS;QAACC,OAAO,eAAEjB,OAAA,CAACN,MAAM;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7Cd,OAAA,CAAChB,KAAK;QAACgC,IAAI,EAAC,qBAAqB;QAACC,OAAO,eAAEjB,OAAA,CAACL,iBAAiB;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpEd,OAAA,CAAChB,KAAK;QAACgC,IAAI,EAAC,kBAAkB;QAACC,OAAO,eAAEjB,OAAA,CAACJ,cAAc;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9Dd,OAAA,CAAChB,KAAK;QAACgC,IAAI,EAAC,oBAAoB;QAACC,OAAO,eAAEjB,OAAA,CAACH,gBAAgB;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC;EAAA,eACT,CAAC;AAEP;AAACV,EAAA,CAhCQD,UAAU;EAAA,QACAlB,WAAW;AAAA;AAAAiC,EAAA,GADrBf,UAAU;AAkCnB,SAASgB,GAAGA,CAAA,EAAG;EACb,oBACEnB,OAAA,CAACF,YAAY;IAAAiB,QAAA,eACXf,OAAA,CAAClB,MAAM;MAAAiC,QAAA,eACLf,OAAA,CAACG,UAAU;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACM,GAAA,GARQD,GAAG;AAUZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}