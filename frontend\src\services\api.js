// API service for making HTTP requests to the backend
const API_BASE_URL = 'http://localhost:3000/api';

class ApiService {
  // Helper method to make HTTP requests
  async makeRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    // Add authorization header if token exists
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Something went wrong');
      }

      return data;
    } catch (error) {
      console.error('API Request Error:', error);
      throw error;
    }
  }

  // Authentication methods
  async login(credentials) {
    return this.makeRequest('/users/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async signup(userData) {
    return this.makeRequest('/users/create', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  // User methods
  async getAllUsers() {
    return this.makeRequest('/users/all');
  }

  async getUserById(id) {
    return this.makeRequest(`/users/${id}`);
  }

  async updateUser(id, userData) {
    return this.makeRequest(`/users/update/${id}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  async deleteUser(id) {
    return this.makeRequest(`/users/delete/${id}`, {
      method: 'DELETE',
    });
  }
}

// Create and export a singleton instance
const apiService = new ApiService();
export default apiService;
