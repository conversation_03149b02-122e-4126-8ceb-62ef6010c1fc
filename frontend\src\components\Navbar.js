import { Link, useNavigate } from "react-router-dom";
import { useState } from "react";
import { useAuth } from "../contexts/AuthContext";
import '../css/components/Navbar.css';

function Navbar() {
  const navigate = useNavigate();
  const { isAuthenticated, user, logout, getDashboardRoute } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const handleLogout = () => {
    logout();
    navigate('/');
    closeMobileMenu();
  };

  return (
    <nav className="modern-navbar">
      <div className="navbar-container">
        <Link to="/" className="navbar-brand" onClick={closeMobileMenu}>
          <span className="brand-icon">💰</span>
          <span className="brand-text">ExpenseTracker</span>
        </Link>

        <div className={`navbar-menu ${isMobileMenuOpen ? 'active' : ''}`}>
          <div className="navbar-nav">
            {!isAuthenticated && (
              <>
                <Link to="/about" className="nav-link" onClick={closeMobileMenu}>About</Link>
                <Link to="/contact" className="nav-link" onClick={closeMobileMenu}>Contact</Link>
              </>
            )}
            {isAuthenticated && (
              <Link
                to={getDashboardRoute(user?.role)}
                className="nav-link"
                onClick={closeMobileMenu}
              >
                Dashboard
              </Link>
            )}
          </div>

          <div className="navbar-auth">
            {!isAuthenticated ? (
              <>
                <Link to="/login" className="auth-btn login-btn" onClick={closeMobileMenu}>
                  Login
                </Link>
                <Link to="/signup" className="auth-btn signup-btn" onClick={closeMobileMenu}>
                  Sign Up
                </Link>
              </>
            ) : (
              <>
                <span className="user-welcome">
                  Welcome, {user?.name || user?.email}
                </span>
                <button className="auth-btn login-btn" onClick={handleLogout}>
                  Logout
                </button>
              </>
            )}
          </div>
        </div>

        <div className="mobile-menu-toggle" onClick={toggleMobileMenu}>
          <span className={isMobileMenuOpen ? 'active' : ''}></span>
          <span className={isMobileMenuOpen ? 'active' : ''}></span>
          <span className={isMobileMenuOpen ? 'active' : ''}></span>
        </div>
      </div>
    </nav>
  );
}

export default Navbar;
