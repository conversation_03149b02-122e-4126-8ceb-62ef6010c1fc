{"ast": null, "code": "// API service for making HTTP requests to the backend\nconst API_BASE_URL = 'http://localhost:3000/api';\nclass ApiService {\n  // Helper method to make HTTP requests\n  async makeRequest(endpoint, options = {}) {\n    const url = `${API_BASE_URL}${endpoint}`;\n    const config = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers\n      },\n      ...options\n    };\n\n    // Add authorization header if token exists\n    const token = localStorage.getItem('authToken');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    try {\n      const response = await fetch(url, config);\n      const data = await response.json();\n      if (!response.ok) {\n        throw new Error(data.message || 'Something went wrong');\n      }\n      return data;\n    } catch (error) {\n      console.error('API Request Error:', error);\n      throw error;\n    }\n  }\n\n  // Authentication methods\n  async login(credentials) {\n    return this.makeRequest('/users/login', {\n      method: 'POST',\n      body: JSON.stringify(credentials)\n    });\n  }\n  async signup(userData) {\n    return this.makeRequest('/users/create', {\n      method: 'POST',\n      body: JSON.stringify(userData)\n    });\n  }\n\n  // User methods\n  async getAllUsers() {\n    return this.makeRequest('/users/all');\n  }\n  async getUserById(id) {\n    return this.makeRequest(`/users/${id}`);\n  }\n  async updateUser(id, userData) {\n    return this.makeRequest(`/users/update/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(userData)\n    });\n  }\n  async deleteUser(id) {\n    return this.makeRequest(`/users/delete/${id}`, {\n      method: 'DELETE'\n    });\n  }\n}\n\n// Create and export a singleton instance\nconst apiService = new ApiService();\nexport default apiService;", "map": {"version": 3, "names": ["API_BASE_URL", "ApiService", "makeRequest", "endpoint", "options", "url", "config", "headers", "token", "localStorage", "getItem", "Authorization", "response", "fetch", "data", "json", "ok", "Error", "message", "error", "console", "login", "credentials", "method", "body", "JSON", "stringify", "signup", "userData", "getAllUsers", "getUserById", "id", "updateUser", "deleteUser", "apiService"], "sources": ["D:/Expense Tracker App (MERN )/frontend/src/services/api.js"], "sourcesContent": ["// API service for making HTTP requests to the backend\nconst API_BASE_URL = 'http://localhost:3000/api';\n\nclass ApiService {\n  // Helper method to make HTTP requests\n  async makeRequest(endpoint, options = {}) {\n    const url = `${API_BASE_URL}${endpoint}`;\n    const config = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    };\n\n    // Add authorization header if token exists\n    const token = localStorage.getItem('authToken');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n\n    try {\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.message || 'Something went wrong');\n      }\n\n      return data;\n    } catch (error) {\n      console.error('API Request Error:', error);\n      throw error;\n    }\n  }\n\n  // Authentication methods\n  async login(credentials) {\n    return this.makeRequest('/users/login', {\n      method: 'POST',\n      body: JSON.stringify(credentials),\n    });\n  }\n\n  async signup(userData) {\n    return this.makeRequest('/users/create', {\n      method: 'POST',\n      body: JSON.stringify(userData),\n    });\n  }\n\n  // User methods\n  async getAllUsers() {\n    return this.makeRequest('/users/all');\n  }\n\n  async getUserById(id) {\n    return this.makeRequest(`/users/${id}`);\n  }\n\n  async updateUser(id, userData) {\n    return this.makeRequest(`/users/update/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(userData),\n    });\n  }\n\n  async deleteUser(id) {\n    return this.makeRequest(`/users/delete/${id}`, {\n      method: 'DELETE',\n    });\n  }\n}\n\n// Create and export a singleton instance\nconst apiService = new ApiService();\nexport default apiService;\n"], "mappings": "AAAA;AACA,MAAMA,YAAY,GAAG,2BAA2B;AAEhD,MAAMC,UAAU,CAAC;EACf;EACA,MAAMC,WAAWA,CAACC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACxC,MAAMC,GAAG,GAAG,GAAGL,YAAY,GAAGG,QAAQ,EAAE;IACxC,MAAMG,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,GAAGH,OAAO,CAACG;MACb,CAAC;MACD,GAAGH;IACL,CAAC;;IAED;IACA,MAAMI,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,IAAIF,KAAK,EAAE;MACTF,MAAM,CAACC,OAAO,CAACI,aAAa,GAAG,UAAUH,KAAK,EAAE;IAClD;IAEA,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAACR,GAAG,EAAEC,MAAM,CAAC;MACzC,MAAMQ,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAACH,IAAI,CAACI,OAAO,IAAI,sBAAsB,CAAC;MACzD;MAEA,OAAOJ,IAAI;IACb,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAME,KAAKA,CAACC,WAAW,EAAE;IACvB,OAAO,IAAI,CAACpB,WAAW,CAAC,cAAc,EAAE;MACtCqB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACJ,WAAW;IAClC,CAAC,CAAC;EACJ;EAEA,MAAMK,MAAMA,CAACC,QAAQ,EAAE;IACrB,OAAO,IAAI,CAAC1B,WAAW,CAAC,eAAe,EAAE;MACvCqB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACE,QAAQ;IAC/B,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMC,WAAWA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC3B,WAAW,CAAC,YAAY,CAAC;EACvC;EAEA,MAAM4B,WAAWA,CAACC,EAAE,EAAE;IACpB,OAAO,IAAI,CAAC7B,WAAW,CAAC,UAAU6B,EAAE,EAAE,CAAC;EACzC;EAEA,MAAMC,UAAUA,CAACD,EAAE,EAAEH,QAAQ,EAAE;IAC7B,OAAO,IAAI,CAAC1B,WAAW,CAAC,iBAAiB6B,EAAE,EAAE,EAAE;MAC7CR,MAAM,EAAE,KAAK;MACbC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACE,QAAQ;IAC/B,CAAC,CAAC;EACJ;EAEA,MAAMK,UAAUA,CAACF,EAAE,EAAE;IACnB,OAAO,IAAI,CAAC7B,WAAW,CAAC,iBAAiB6B,EAAE,EAAE,EAAE;MAC7CR,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,MAAMW,UAAU,GAAG,IAAIjC,UAAU,CAAC,CAAC;AACnC,eAAeiC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}