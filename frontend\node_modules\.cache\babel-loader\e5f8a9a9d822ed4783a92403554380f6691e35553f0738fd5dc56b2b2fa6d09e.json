{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\frontend\\\\src\\\\pages\\\\Signup.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport '../css/pages/Auth.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Signup() {\n  _s();\n  const navigate = useNavigate();\n  const {\n    signup\n  } = useAuth();\n  const [formData, setFormData] = useState({\n    username: '',\n    // Changed from 'name' to match backend\n    email: '',\n    role: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [isLoading, setIsLoading] = useState(false);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    } else if (formData.username.trim().length < 3) {\n      newErrors.username = 'Username must be at least 3 characters';\n    }\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!formData.role) {\n      newErrors.role = 'Role is required';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n    if (!formData.confirmPassword) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    setIsLoading(true);\n    try {\n      // Prepare data for API (exclude confirmPassword)\n      const {\n        confirmPassword,\n        ...signupData\n      } = formData;\n      const result = await signup(signupData);\n      if (result.success) {\n        alert('Account created successfully! Please login to continue.');\n        navigate('/login');\n      } else {\n        setErrors({\n          general: result.error\n        });\n      }\n    } catch (error) {\n      console.error('Signup error:', error);\n      setErrors({\n        general: 'Signup failed. Please try again.'\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-background\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-particles\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-content signup\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"auth-title\",\n            children: [\"Create \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gradient-text\",\n              children: \"Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 36\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"auth-subtitle\",\n            children: \"Join thousands of users managing their finances smarter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"auth-form signup\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"username\",\n              className: \"form-label\",\n              children: \"Username\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"username\",\n              name: \"username\",\n              value: formData.username,\n              onChange: handleChange,\n              className: `form-input ${errors.username ? 'error' : ''}`,\n              placeholder: \"Enter your username\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 29\n            }, this), errors.username && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"error-message\",\n              children: errors.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 49\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              className: \"form-label\",\n              children: \"Email Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              id: \"email\",\n              name: \"email\",\n              value: formData.email,\n              onChange: handleChange,\n              className: `form-input ${errors.email ? 'error' : ''}`,\n              placeholder: \"Enter your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 29\n            }, this), errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"error-message\",\n              children: errors.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 46\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"form-label\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              id: \"password\",\n              name: \"password\",\n              value: formData.password,\n              onChange: handleChange,\n              className: `form-input ${errors.password ? 'error' : ''}`,\n              placeholder: \"Create a password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 29\n            }, this), errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"error-message\",\n              children: errors.password\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 49\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"confirmPassword\",\n              className: \"form-label\",\n              children: \"Confirm Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              id: \"confirmPassword\",\n              name: \"confirmPassword\",\n              value: formData.confirmPassword,\n              onChange: handleChange,\n              className: `form-input ${errors.confirmPassword ? 'error' : ''}`,\n              placeholder: \"Confirm your password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 29\n            }, this), errors.confirmPassword && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"error-message\",\n              children: errors.confirmPassword\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 56\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group full-width\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"role\",\n              className: \"form-label\",\n              children: \"Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"role\",\n              name: \"role\",\n              value: formData.role,\n              onChange: handleChange,\n              className: `form-input form-select ${errors.role ? 'error' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select your role\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"employee\",\n                children: \"Employee\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"finance\",\n                children: \"Finance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 29\n            }, this), errors.role && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"error-message\",\n              children: errors.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 45\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-options\",\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"checkbox-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"checkmark\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 33\n              }, this), \"I agree to the \", /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/terms\",\n                className: \"terms-link\",\n                children: \"Terms & Conditions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 48\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: `auth-submit-btn ${isLoading ? 'loading' : ''}`,\n            disabled: isLoading,\n            children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"loading-spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 37\n              }, this), \"Creating Account...\"]\n            }, void 0, true) : 'Create Account'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"auth-link\",\n              children: \"Sign in here\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 9\n  }, this);\n}\n_s(Signup, \"N4AarXMOwjN3zkR6WWCRGakzyzs=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Signup;\nexport default Signup;\nvar _c;\n$RefreshReg$(_c, \"Signup\");", "map": {"version": 3, "names": ["useState", "Link", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Signup", "_s", "navigate", "signup", "formData", "setFormData", "username", "email", "role", "password", "confirmPassword", "errors", "setErrors", "isLoading", "setIsLoading", "handleChange", "e", "name", "value", "target", "prev", "validateForm", "newErrors", "trim", "length", "test", "Object", "keys", "handleSubmit", "preventDefault", "signupData", "result", "success", "alert", "general", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "required", "to", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/frontend/src/pages/Signup.js"], "sourcesContent": ["import { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport '../css/pages/Auth.css';\n\nfunction Signup() {\n    const navigate = useNavigate();\n    const { signup } = useAuth();\n\n    const [formData, setFormData] = useState({\n        username: '', // Changed from 'name' to match backend\n        email: '',\n        role: '',\n        password: '',\n        confirmPassword: ''\n    });\n\n    const [errors, setErrors] = useState({});\n    const [isLoading, setIsLoading] = useState(false);\n\n    const handleChange = (e) => {\n        const { name, value } = e.target;\n        setFormData(prev => ({\n            ...prev,\n            [name]: value\n        }));\n        // Clear error when user starts typing\n        if (errors[name]) {\n            setErrors(prev => ({\n                ...prev,\n                [name]: ''\n            }));\n        }\n    };\n\n    const validateForm = () => {\n        const newErrors = {};\n\n        if (!formData.username.trim()) {\n            newErrors.username = 'Username is required';\n        } else if (formData.username.trim().length < 3) {\n            newErrors.username = 'Username must be at least 3 characters';\n        }\n\n        if (!formData.email) {\n            newErrors.email = 'Email is required';\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = 'Email is invalid';\n        }\n\n        if (!formData.role) {\n            newErrors.role = 'Role is required';\n        }\n\n        if (!formData.password) {\n            newErrors.password = 'Password is required';\n        } else if (formData.password.length < 6) {\n            newErrors.password = 'Password must be at least 6 characters';\n        }\n\n        if (!formData.confirmPassword) {\n            newErrors.confirmPassword = 'Please confirm your password';\n        } else if (formData.password !== formData.confirmPassword) {\n            newErrors.confirmPassword = 'Passwords do not match';\n        }\n\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        \n        if (!validateForm()) {\n            return;\n        }\n\n        setIsLoading(true);\n        \n        try {\n            // Prepare data for API (exclude confirmPassword)\n            const { confirmPassword, ...signupData } = formData;\n\n            const result = await signup(signupData);\n\n            if (result.success) {\n                alert('Account created successfully! Please login to continue.');\n                navigate('/login');\n            } else {\n                setErrors({ general: result.error });\n            }\n        } catch (error) {\n            console.error('Signup error:', error);\n            setErrors({ general: 'Signup failed. Please try again.' });\n        } finally {\n            setIsLoading(false);\n        }\n    };\n\n    return (\n        <div className=\"auth-container\">\n            <div className=\"auth-background\">\n                <div className=\"auth-particles\"></div>\n            </div>\n            \n            <div className=\"auth-content signup\">\n                <div className=\"auth-card\">\n                    <div className=\"auth-header\">\n                        <h1 className=\"auth-title\">\n                            Create <span className=\"gradient-text\">Account</span>\n                        </h1>\n                        <p className=\"auth-subtitle\">\n                            Join thousands of users managing their finances smarter\n                        </p>\n                    </div>\n\n                    <form onSubmit={handleSubmit} className=\"auth-form signup\">\n                        <div className=\"form-group\">\n                            <label htmlFor=\"username\" className=\"form-label\">\n                                Username\n                            </label>\n                            <input\n                                type=\"text\"\n                                id=\"username\"\n                                name=\"username\"\n                                value={formData.username}\n                                onChange={handleChange}\n                                className={`form-input ${errors.username ? 'error' : ''}`}\n                                placeholder=\"Enter your username\"\n                            />\n                            {errors.username && <span className=\"error-message\">{errors.username}</span>}\n                        </div>\n\n                        <div className=\"form-group\">\n                            <label htmlFor=\"email\" className=\"form-label\">\n                                Email Address\n                            </label>\n                            <input\n                                type=\"email\"\n                                id=\"email\"\n                                name=\"email\"\n                                value={formData.email}\n                                onChange={handleChange}\n                                className={`form-input ${errors.email ? 'error' : ''}`}\n                                placeholder=\"Enter your email\"\n                            />\n                            {errors.email && <span className=\"error-message\">{errors.email}</span>}\n                        </div>\n\n                        <div className=\"form-group\">\n                            <label htmlFor=\"password\" className=\"form-label\">\n                                Password\n                            </label>\n                            <input\n                                type=\"password\"\n                                id=\"password\"\n                                name=\"password\"\n                                value={formData.password}\n                                onChange={handleChange}\n                                className={`form-input ${errors.password ? 'error' : ''}`}\n                                placeholder=\"Create a password\"\n                            />\n                            {errors.password && <span className=\"error-message\">{errors.password}</span>}\n                        </div>\n\n                        <div className=\"form-group\">\n                            <label htmlFor=\"confirmPassword\" className=\"form-label\">\n                                Confirm Password\n                            </label>\n                            <input\n                                type=\"password\"\n                                id=\"confirmPassword\"\n                                name=\"confirmPassword\"\n                                value={formData.confirmPassword}\n                                onChange={handleChange}\n                                className={`form-input ${errors.confirmPassword ? 'error' : ''}`}\n                                placeholder=\"Confirm your password\"\n                            />\n                            {errors.confirmPassword && <span className=\"error-message\">{errors.confirmPassword}</span>}\n                        </div>\n\n                        <div className=\"form-group full-width\">\n                            <label htmlFor=\"role\" className=\"form-label\">\n                                Role\n                            </label>\n                            <select\n                                id=\"role\"\n                                name=\"role\"\n                                value={formData.role}\n                                onChange={handleChange}\n                                className={`form-input form-select ${errors.role ? 'error' : ''}`}\n                            >\n                                <option value=\"\">Select your role</option>\n                                <option value=\"employee\">Employee</option>\n                                <option value=\"finance\">Finance</option>\n                            </select>\n                            {errors.role && <span className=\"error-message\">{errors.role}</span>}\n                        </div>\n\n                        <div className=\"form-options\">\n                            <label className=\"checkbox-container\">\n                                <input type=\"checkbox\" required />\n                                <span className=\"checkmark\"></span>\n                                I agree to the <Link to=\"/terms\" className=\"terms-link\">Terms & Conditions</Link>\n                            </label>\n                        </div>\n\n                        <button \n                            type=\"submit\" \n                            className={`auth-submit-btn ${isLoading ? 'loading' : ''}`}\n                            disabled={isLoading}\n                        >\n                            {isLoading ? (\n                                <>\n                                    <span className=\"loading-spinner\"></span>\n                                    Creating Account...\n                                </>\n                            ) : (\n                                'Create Account'\n                            )}\n                        </button>\n                    </form>\n\n                    <div className=\"auth-footer\">\n                        <p>\n                            Already have an account?{' '}\n                            <Link to=\"/login\" className=\"auth-link\">\n                                Sign in here\n                            </Link>\n                        </p>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n}\n\nexport default Signup;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EACd,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES;EAAO,CAAC,GAAGR,OAAO,CAAC,CAAC;EAE5B,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC;IACrCc,QAAQ,EAAE,EAAE;IAAE;IACdC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACrB,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMuB,YAAY,GAAIC,CAAC,IAAK;IACxB,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCd,WAAW,CAACe,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACZ,CAAC,CAAC,CAAC;IACH;IACA,IAAIP,MAAM,CAACM,IAAI,CAAC,EAAE;MACdL,SAAS,CAACQ,IAAI,KAAK;QACf,GAAGA,IAAI;QACP,CAACH,IAAI,GAAG;MACZ,CAAC,CAAC,CAAC;IACP;EACJ,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAClB,QAAQ,CAACE,QAAQ,CAACiB,IAAI,CAAC,CAAC,EAAE;MAC3BD,SAAS,CAAChB,QAAQ,GAAG,sBAAsB;IAC/C,CAAC,MAAM,IAAIF,QAAQ,CAACE,QAAQ,CAACiB,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MAC5CF,SAAS,CAAChB,QAAQ,GAAG,wCAAwC;IACjE;IAEA,IAAI,CAACF,QAAQ,CAACG,KAAK,EAAE;MACjBe,SAAS,CAACf,KAAK,GAAG,mBAAmB;IACzC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACkB,IAAI,CAACrB,QAAQ,CAACG,KAAK,CAAC,EAAE;MAC7Ce,SAAS,CAACf,KAAK,GAAG,kBAAkB;IACxC;IAEA,IAAI,CAACH,QAAQ,CAACI,IAAI,EAAE;MAChBc,SAAS,CAACd,IAAI,GAAG,kBAAkB;IACvC;IAEA,IAAI,CAACJ,QAAQ,CAACK,QAAQ,EAAE;MACpBa,SAAS,CAACb,QAAQ,GAAG,sBAAsB;IAC/C,CAAC,MAAM,IAAIL,QAAQ,CAACK,QAAQ,CAACe,MAAM,GAAG,CAAC,EAAE;MACrCF,SAAS,CAACb,QAAQ,GAAG,wCAAwC;IACjE;IAEA,IAAI,CAACL,QAAQ,CAACM,eAAe,EAAE;MAC3BY,SAAS,CAACZ,eAAe,GAAG,8BAA8B;IAC9D,CAAC,MAAM,IAAIN,QAAQ,CAACK,QAAQ,KAAKL,QAAQ,CAACM,eAAe,EAAE;MACvDY,SAAS,CAACZ,eAAe,GAAG,wBAAwB;IACxD;IAEAE,SAAS,CAACU,SAAS,CAAC;IACpB,OAAOI,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAACE,MAAM,KAAK,CAAC;EAC9C,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOZ,CAAC,IAAK;IAC9BA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAI,CAACR,YAAY,CAAC,CAAC,EAAE;MACjB;IACJ;IAEAP,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACA;MACA,MAAM;QAAEJ,eAAe;QAAE,GAAGoB;MAAW,CAAC,GAAG1B,QAAQ;MAEnD,MAAM2B,MAAM,GAAG,MAAM5B,MAAM,CAAC2B,UAAU,CAAC;MAEvC,IAAIC,MAAM,CAACC,OAAO,EAAE;QAChBC,KAAK,CAAC,yDAAyD,CAAC;QAChE/B,QAAQ,CAAC,QAAQ,CAAC;MACtB,CAAC,MAAM;QACHU,SAAS,CAAC;UAAEsB,OAAO,EAAEH,MAAM,CAACI;QAAM,CAAC,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCvB,SAAS,CAAC;QAAEsB,OAAO,EAAE;MAAmC,CAAC,CAAC;IAC9D,CAAC,SAAS;MACNpB,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EAED,oBACIjB,OAAA;IAAKwC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC3BzC,OAAA;MAAKwC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC5BzC,OAAA;QAAKwC,SAAS,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eAEN7C,OAAA;MAAKwC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAChCzC,OAAA;QAAKwC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtBzC,OAAA;UAAKwC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBzC,OAAA;YAAIwC,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,SAChB,eAAAzC,OAAA;cAAMwC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACL7C,OAAA;YAAGwC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7C,OAAA;UAAM8C,QAAQ,EAAEf,YAAa;UAACS,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBACtDzC,OAAA;YAAKwC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBzC,OAAA;cAAO+C,OAAO,EAAC,UAAU;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEjD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7C,OAAA;cACIgD,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,UAAU;cACb7B,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEd,QAAQ,CAACE,QAAS;cACzByC,QAAQ,EAAEhC,YAAa;cACvBsB,SAAS,EAAE,cAAc1B,MAAM,CAACL,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;cAC1D0C,WAAW,EAAC;YAAqB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,EACD/B,MAAM,CAACL,QAAQ,iBAAIT,OAAA;cAAMwC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAE3B,MAAM,CAACL;YAAQ;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eAEN7C,OAAA;YAAKwC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBzC,OAAA;cAAO+C,OAAO,EAAC,OAAO;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAE9C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7C,OAAA;cACIgD,IAAI,EAAC,OAAO;cACZC,EAAE,EAAC,OAAO;cACV7B,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEd,QAAQ,CAACG,KAAM;cACtBwC,QAAQ,EAAEhC,YAAa;cACvBsB,SAAS,EAAE,cAAc1B,MAAM,CAACJ,KAAK,GAAG,OAAO,GAAG,EAAE,EAAG;cACvDyC,WAAW,EAAC;YAAkB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,EACD/B,MAAM,CAACJ,KAAK,iBAAIV,OAAA;cAAMwC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAE3B,MAAM,CAACJ;YAAK;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eAEN7C,OAAA;YAAKwC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBzC,OAAA;cAAO+C,OAAO,EAAC,UAAU;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEjD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7C,OAAA;cACIgD,IAAI,EAAC,UAAU;cACfC,EAAE,EAAC,UAAU;cACb7B,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEd,QAAQ,CAACK,QAAS;cACzBsC,QAAQ,EAAEhC,YAAa;cACvBsB,SAAS,EAAE,cAAc1B,MAAM,CAACF,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;cAC1DuC,WAAW,EAAC;YAAmB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,EACD/B,MAAM,CAACF,QAAQ,iBAAIZ,OAAA;cAAMwC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAE3B,MAAM,CAACF;YAAQ;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eAEN7C,OAAA;YAAKwC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBzC,OAAA;cAAO+C,OAAO,EAAC,iBAAiB;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7C,OAAA;cACIgD,IAAI,EAAC,UAAU;cACfC,EAAE,EAAC,iBAAiB;cACpB7B,IAAI,EAAC,iBAAiB;cACtBC,KAAK,EAAEd,QAAQ,CAACM,eAAgB;cAChCqC,QAAQ,EAAEhC,YAAa;cACvBsB,SAAS,EAAE,cAAc1B,MAAM,CAACD,eAAe,GAAG,OAAO,GAAG,EAAE,EAAG;cACjEsC,WAAW,EAAC;YAAuB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,EACD/B,MAAM,CAACD,eAAe,iBAAIb,OAAA;cAAMwC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAE3B,MAAM,CAACD;YAAe;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF,CAAC,eAEN7C,OAAA;YAAKwC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBAClCzC,OAAA;cAAO+C,OAAO,EAAC,MAAM;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7C,OAAA;cACIiD,EAAE,EAAC,MAAM;cACT7B,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEd,QAAQ,CAACI,IAAK;cACrBuC,QAAQ,EAAEhC,YAAa;cACvBsB,SAAS,EAAE,0BAA0B1B,MAAM,CAACH,IAAI,GAAG,OAAO,GAAG,EAAE,EAAG;cAAA8B,QAAA,gBAElEzC,OAAA;gBAAQqB,KAAK,EAAC,EAAE;gBAAAoB,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1C7C,OAAA;gBAAQqB,KAAK,EAAC,UAAU;gBAAAoB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1C7C,OAAA;gBAAQqB,KAAK,EAAC,SAAS;gBAAAoB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,EACR/B,MAAM,CAACH,IAAI,iBAAIX,OAAA;cAAMwC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAE3B,MAAM,CAACH;YAAI;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eAEN7C,OAAA;YAAKwC,SAAS,EAAC,cAAc;YAAAC,QAAA,eACzBzC,OAAA;cAAOwC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjCzC,OAAA;gBAAOgD,IAAI,EAAC,UAAU;gBAACI,QAAQ;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClC7C,OAAA;gBAAMwC,SAAS,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,mBACpB,eAAA7C,OAAA,CAACJ,IAAI;gBAACyD,EAAE,EAAC,QAAQ;gBAACb,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAEN7C,OAAA;YACIgD,IAAI,EAAC,QAAQ;YACbR,SAAS,EAAE,mBAAmBxB,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;YAC3DsC,QAAQ,EAAEtC,SAAU;YAAAyB,QAAA,EAEnBzB,SAAS,gBACNhB,OAAA,CAAAE,SAAA;cAAAuC,QAAA,gBACIzC,OAAA;gBAAMwC,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,uBAE7C;YAAA,eAAE,CAAC,GAEH;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAEP7C,OAAA;UAAKwC,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxBzC,OAAA;YAAAyC,QAAA,GAAG,0BACyB,EAAC,GAAG,eAC5BzC,OAAA,CAACJ,IAAI;cAACyD,EAAE,EAAC,QAAQ;cAACb,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAExC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAACzC,EAAA,CAtOQD,MAAM;EAAA,QACMN,WAAW,EACTC,OAAO;AAAA;AAAAyD,EAAA,GAFrBpD,MAAM;AAwOf,eAAeA,MAAM;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}