{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport apiService from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Check if user is already logged in on app start\n  useEffect(() => {\n    const checkAuthStatus = () => {\n      const token = localStorage.getItem('authToken');\n      const userData = localStorage.getItem('userData');\n      if (token && userData) {\n        try {\n          const parsedUser = JSON.parse(userData);\n          setUser(parsedUser);\n          setIsAuthenticated(true);\n        } catch (error) {\n          console.error('Error parsing user data:', error);\n          // Clear invalid data\n          localStorage.removeItem('authToken');\n          localStorage.removeItem('userData');\n        }\n      }\n      setIsLoading(false);\n    };\n    checkAuthStatus();\n  }, []);\n  const login = async credentials => {\n    try {\n      setIsLoading(true);\n      const response = await apiService.login(credentials);\n      const {\n        token,\n        user: userData\n      } = response;\n\n      // Store token and user data\n      localStorage.setItem('authToken', token);\n      localStorage.setItem('userData', JSON.stringify(userData));\n      setUser(userData);\n      setIsAuthenticated(true);\n      return {\n        success: true,\n        user: userData\n      };\n    } catch (error) {\n      console.error('Login error:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const signup = async userData => {\n    try {\n      setIsLoading(true);\n      const response = await apiService.signup(userData);\n\n      // After successful signup, you might want to automatically log them in\n      // or redirect to login page\n      return {\n        success: true,\n        data: response\n      };\n    } catch (error) {\n      console.error('Signup error:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const logout = () => {\n    // Clear stored data\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('userData');\n\n    // Reset state\n    setUser(null);\n    setIsAuthenticated(false);\n  };\n\n  // Get dashboard route based on user role\n  const getDashboardRoute = userRole => {\n    switch (userRole) {\n      case 'admin':\n        return '/dashboard/admin';\n      case 'finance':\n        return '/dashboard/finance';\n      case 'employee':\n      default:\n        return '/dashboard/employee';\n    }\n  };\n  const value = {\n    user,\n    isAuthenticated,\n    isLoading,\n    login,\n    signup,\n    logout,\n    getDashboardRoute\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"v1LRzRO747hYRtRdhUIJv9BiM4M=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "apiService", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "isAuthenticated", "setIsAuthenticated", "isLoading", "setIsLoading", "checkAuthStatus", "token", "localStorage", "getItem", "userData", "parsedUser", "JSON", "parse", "error", "console", "removeItem", "login", "credentials", "response", "setItem", "stringify", "success", "message", "signup", "data", "logout", "getDashboardRoute", "userRole", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport apiService from '../services/api';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Check if user is already logged in on app start\n  useEffect(() => {\n    const checkAuthStatus = () => {\n      const token = localStorage.getItem('authToken');\n      const userData = localStorage.getItem('userData');\n      \n      if (token && userData) {\n        try {\n          const parsedUser = JSON.parse(userData);\n          setUser(parsedUser);\n          setIsAuthenticated(true);\n        } catch (error) {\n          console.error('Error parsing user data:', error);\n          // Clear invalid data\n          localStorage.removeItem('authToken');\n          localStorage.removeItem('userData');\n        }\n      }\n      setIsLoading(false);\n    };\n\n    checkAuthStatus();\n  }, []);\n\n  const login = async (credentials) => {\n    try {\n      setIsLoading(true);\n      const response = await apiService.login(credentials);\n      \n      const { token, user: userData } = response;\n      \n      // Store token and user data\n      localStorage.setItem('authToken', token);\n      localStorage.setItem('userData', JSON.stringify(userData));\n      \n      setUser(userData);\n      setIsAuthenticated(true);\n      \n      return { success: true, user: userData };\n    } catch (error) {\n      console.error('Login error:', error);\n      return { success: false, error: error.message };\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const signup = async (userData) => {\n    try {\n      setIsLoading(true);\n      const response = await apiService.signup(userData);\n      \n      // After successful signup, you might want to automatically log them in\n      // or redirect to login page\n      return { success: true, data: response };\n    } catch (error) {\n      console.error('Signup error:', error);\n      return { success: false, error: error.message };\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = () => {\n    // Clear stored data\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('userData');\n    \n    // Reset state\n    setUser(null);\n    setIsAuthenticated(false);\n  };\n\n  // Get dashboard route based on user role\n  const getDashboardRoute = (userRole) => {\n    switch (userRole) {\n      case 'admin':\n        return '/dashboard/admin';\n      case 'finance':\n        return '/dashboard/finance';\n      case 'employee':\n      default:\n        return '/dashboard/employee';\n    }\n  };\n\n  const value = {\n    user,\n    isAuthenticated,\n    isLoading,\n    login,\n    signup,\n    logout,\n    getDashboardRoute,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,UAAU,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MAEjD,IAAIF,KAAK,IAAIG,QAAQ,EAAE;QACrB,IAAI;UACF,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;UACvCT,OAAO,CAACU,UAAU,CAAC;UACnBR,kBAAkB,CAAC,IAAI,CAAC;QAC1B,CAAC,CAAC,OAAOW,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChD;UACAN,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC;UACpCR,YAAY,CAACQ,UAAU,CAAC,UAAU,CAAC;QACrC;MACF;MACAX,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC;IAEDC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACFb,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMc,QAAQ,GAAG,MAAM9B,UAAU,CAAC4B,KAAK,CAACC,WAAW,CAAC;MAEpD,MAAM;QAAEX,KAAK;QAAEP,IAAI,EAAEU;MAAS,CAAC,GAAGS,QAAQ;;MAE1C;MACAX,YAAY,CAACY,OAAO,CAAC,WAAW,EAAEb,KAAK,CAAC;MACxCC,YAAY,CAACY,OAAO,CAAC,UAAU,EAAER,IAAI,CAACS,SAAS,CAACX,QAAQ,CAAC,CAAC;MAE1DT,OAAO,CAACS,QAAQ,CAAC;MACjBP,kBAAkB,CAAC,IAAI,CAAC;MAExB,OAAO;QAAEmB,OAAO,EAAE,IAAI;QAAEtB,IAAI,EAAEU;MAAS,CAAC;IAC1C,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,OAAO;QAAEQ,OAAO,EAAE,KAAK;QAAER,KAAK,EAAEA,KAAK,CAACS;MAAQ,CAAC;IACjD,CAAC,SAAS;MACRlB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMmB,MAAM,GAAG,MAAOd,QAAQ,IAAK;IACjC,IAAI;MACFL,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMc,QAAQ,GAAG,MAAM9B,UAAU,CAACmC,MAAM,CAACd,QAAQ,CAAC;;MAElD;MACA;MACA,OAAO;QAAEY,OAAO,EAAE,IAAI;QAAEG,IAAI,EAAEN;MAAS,CAAC;IAC1C,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,OAAO;QAAEQ,OAAO,EAAE,KAAK;QAAER,KAAK,EAAEA,KAAK,CAACS;MAAQ,CAAC;IACjD,CAAC,SAAS;MACRlB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMqB,MAAM,GAAGA,CAAA,KAAM;IACnB;IACAlB,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC;IACpCR,YAAY,CAACQ,UAAU,CAAC,UAAU,CAAC;;IAEnC;IACAf,OAAO,CAAC,IAAI,CAAC;IACbE,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMwB,iBAAiB,GAAIC,QAAQ,IAAK;IACtC,QAAQA,QAAQ;MACd,KAAK,OAAO;QACV,OAAO,kBAAkB;MAC3B,KAAK,SAAS;QACZ,OAAO,oBAAoB;MAC7B,KAAK,UAAU;MACf;QACE,OAAO,qBAAqB;IAChC;EACF,CAAC;EAED,MAAMC,KAAK,GAAG;IACZ7B,IAAI;IACJE,eAAe;IACfE,SAAS;IACTa,KAAK;IACLO,MAAM;IACNE,MAAM;IACNC;EACF,CAAC;EAED,oBACEpC,OAAA,CAACC,WAAW,CAACsC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA/B,QAAA,EAChCA;EAAQ;IAAAiC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACnC,GAAA,CA1GWF,YAAY;AAAAsC,EAAA,GAAZtC,YAAY;AAAA,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}