{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\frontend\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { useState } from \"react\";\nimport { useAuth } from \"../contexts/AuthContext\";\nimport '../css/components/Navbar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Navbar() {\n  _s();\n  const navigate = useNavigate();\n  const {\n    isAuthenticated,\n    user,\n    logout,\n    getDashboardRoute\n  } = useAuth();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen);\n  };\n  const closeMobileMenu = () => {\n    setIsMobileMenuOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"modern-navbar\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-container\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"navbar-brand\",\n        onClick: closeMobileMenu,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"brand-icon\",\n          children: \"\\uD83D\\uDCB0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"brand-text\",\n          children: \"ExpenseTracker\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `navbar-menu ${isMobileMenuOpen ? 'active' : ''}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"navbar-nav\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            className: \"nav-link\",\n            onClick: closeMobileMenu,\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contact\",\n            className: \"nav-link\",\n            onClick: closeMobileMenu,\n            children: \"Contact\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"navbar-auth\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"auth-btn login-btn\",\n            onClick: closeMobileMenu,\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/signup\",\n            className: \"auth-btn signup-btn\",\n            onClick: closeMobileMenu,\n            children: \"Sign Up\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mobile-menu-toggle\",\n        onClick: toggleMobileMenu,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: isMobileMenuOpen ? 'active' : ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: isMobileMenuOpen ? 'active' : ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: isMobileMenuOpen ? 'active' : ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n}\n_s(Navbar, \"EOxE0IZuM+A7PRPJHRcjpF0d398=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["Link", "useNavigate", "useState", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "navigate", "isAuthenticated", "user", "logout", "getDashboardRoute", "isMobileMenuOpen", "setIsMobileMenuOpen", "toggleMobileMenu", "closeMobileMenu", "className", "children", "to", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/frontend/src/components/Navbar.js"], "sourcesContent": ["import { Link, useNavigate } from \"react-router-dom\";\r\nimport { useState } from \"react\";\r\nimport { useAuth } from \"../contexts/AuthContext\";\r\nimport '../css/components/Navbar.css';\r\n\r\nfunction Navbar() {\r\n  const navigate = useNavigate();\r\n  const { isAuthenticated, user, logout, getDashboardRoute } = useAuth();\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n\r\n  const toggleMobileMenu = () => {\r\n    setIsMobileMenuOpen(!isMobileMenuOpen);\r\n  };\r\n\r\n  const closeMobileMenu = () => {\r\n    setIsMobileMenuOpen(false);\r\n  };\r\n\r\n  return (\r\n    <nav className=\"modern-navbar\">\r\n      <div className=\"navbar-container\">\r\n        <Link to=\"/\" className=\"navbar-brand\" onClick={closeMobileMenu}>\r\n          <span className=\"brand-icon\">💰</span>\r\n          <span className=\"brand-text\">ExpenseTracker</span>\r\n        </Link>\r\n\r\n        <div className={`navbar-menu ${isMobileMenuOpen ? 'active' : ''}`}>\r\n          <div className=\"navbar-nav\">\r\n            <Link to=\"/about\" className=\"nav-link\" onClick={closeMobileMenu}>About</Link>\r\n            <Link to=\"/contact\" className=\"nav-link\" onClick={closeMobileMenu}>Contact</Link>\r\n          </div>\r\n\r\n          <div className=\"navbar-auth\">\r\n            <Link to=\"/login\" className=\"auth-btn login-btn\" onClick={closeMobileMenu}>\r\n              Login\r\n            </Link>\r\n            <Link to=\"/signup\" className=\"auth-btn signup-btn\" onClick={closeMobileMenu}>\r\n              Sign Up\r\n            </Link>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"mobile-menu-toggle\" onClick={toggleMobileMenu}>\r\n          <span className={isMobileMenuOpen ? 'active' : ''}></span>\r\n          <span className={isMobileMenuOpen ? 'active' : ''}></span>\r\n          <span className={isMobileMenuOpen ? 'active' : ''}></span>\r\n        </div>\r\n      </div>\r\n    </nav>\r\n  );\r\n}\r\n\r\nexport default Navbar;\r\n"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,QAAQ,QAAQ,OAAO;AAChC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAO,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEQ,eAAe;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAkB,CAAC,GAAGT,OAAO,CAAC,CAAC;EACtE,MAAM,CAACU,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMa,gBAAgB,GAAGA,CAAA,KAAM;IAC7BD,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;EACxC,CAAC;EAED,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5BF,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,oBACET,OAAA;IAAKY,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5Bb,OAAA;MAAKY,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/Bb,OAAA,CAACL,IAAI;QAACmB,EAAE,EAAC,GAAG;QAACF,SAAS,EAAC,cAAc;QAACG,OAAO,EAAEJ,eAAgB;QAAAE,QAAA,gBAC7Db,OAAA;UAAMY,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtCnB,OAAA;UAAMY,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eAEPnB,OAAA;QAAKY,SAAS,EAAE,eAAeJ,gBAAgB,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAAK,QAAA,gBAChEb,OAAA;UAAKY,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBb,OAAA,CAACL,IAAI;YAACmB,EAAE,EAAC,QAAQ;YAACF,SAAS,EAAC,UAAU;YAACG,OAAO,EAAEJ,eAAgB;YAAAE,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7EnB,OAAA,CAACL,IAAI;YAACmB,EAAE,EAAC,UAAU;YAACF,SAAS,EAAC,UAAU;YAACG,OAAO,EAAEJ,eAAgB;YAAAE,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eAENnB,OAAA;UAAKY,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1Bb,OAAA,CAACL,IAAI;YAACmB,EAAE,EAAC,QAAQ;YAACF,SAAS,EAAC,oBAAoB;YAACG,OAAO,EAAEJ,eAAgB;YAAAE,QAAA,EAAC;UAE3E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPnB,OAAA,CAACL,IAAI;YAACmB,EAAE,EAAC,SAAS;YAACF,SAAS,EAAC,qBAAqB;YAACG,OAAO,EAAEJ,eAAgB;YAAAE,QAAA,EAAC;UAE7E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnB,OAAA;QAAKY,SAAS,EAAC,oBAAoB;QAACG,OAAO,EAAEL,gBAAiB;QAAAG,QAAA,gBAC5Db,OAAA;UAAMY,SAAS,EAAEJ,gBAAgB,GAAG,QAAQ,GAAG;QAAG;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1DnB,OAAA;UAAMY,SAAS,EAAEJ,gBAAgB,GAAG,QAAQ,GAAG;QAAG;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1DnB,OAAA;UAAMY,SAAS,EAAEJ,gBAAgB,GAAG,QAAQ,GAAG;QAAG;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACjB,EAAA,CA7CQD,MAAM;EAAA,QACIL,WAAW,EACiCE,OAAO;AAAA;AAAAsB,EAAA,GAF7DnB,MAAM;AA+Cf,eAAeA,MAAM;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}