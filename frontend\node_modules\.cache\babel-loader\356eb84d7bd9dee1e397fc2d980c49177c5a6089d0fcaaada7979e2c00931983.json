{"ast": null, "code": "var _jsxFileName = \"D:\\\\Expense Tracker App (MERN )\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  allowedRoles = []\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    user,\n    isLoading\n  } = useAuth();\n\n  // Show loading while checking authentication status\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        fontSize: '1.2rem'\n      },\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Check if user has required role (if specified)\n  if (allowedRoles.length > 0 && !allowedRoles.includes(user === null || user === void 0 ? void 0 : user.role)) {\n    // Redirect to appropriate dashboard based on user's actual role\n    const userDashboard = `/dashboard/${(user === null || user === void 0 ? void 0 : user.role) || 'employee'}`;\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: userDashboard,\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 12\n    }, this);\n  }\n  return children;\n};\n_s(ProtectedRoute, \"r/38E3umCJQz9nJgL/a0pLRGLAM=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["Navigate", "useAuth", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "allowedRoles", "_s", "isAuthenticated", "user", "isLoading", "style", "display", "justifyContent", "alignItems", "height", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "length", "includes", "role", "userDashboard", "_c", "$RefreshReg$"], "sources": ["D:/Expense Tracker App (MERN )/frontend/src/components/ProtectedRoute.js"], "sourcesContent": ["import { Navigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst ProtectedRoute = ({ children, allowedRoles = [] }) => {\n  const { isAuthenticated, user, isLoading } = useAuth();\n\n  // Show loading while checking authentication status\n  if (isLoading) {\n    return (\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'center', \n        alignItems: 'center', \n        height: '100vh',\n        fontSize: '1.2rem'\n      }}>\n        Loading...\n      </div>\n    );\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  // Check if user has required role (if specified)\n  if (allowedRoles.length > 0 && !allowedRoles.includes(user?.role)) {\n    // Redirect to appropriate dashboard based on user's actual role\n    const userDashboard = `/dashboard/${user?.role || 'employee'}`;\n    return <Navigate to={userDashboard} replace />;\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,YAAY,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAU,CAAC,GAAGT,OAAO,CAAC,CAAC;;EAEtD;EACA,IAAIS,SAAS,EAAE;IACb,oBACEP,OAAA;MAAKQ,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,QAAQ,EAAE;MACZ,CAAE;MAAAX,QAAA,EAAC;IAEH;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;;EAEA;EACA,IAAI,CAACZ,eAAe,EAAE;IACpB,oBAAOL,OAAA,CAACH,QAAQ;MAACqB,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;;EAEA;EACA,IAAId,YAAY,CAACiB,MAAM,GAAG,CAAC,IAAI,CAACjB,YAAY,CAACkB,QAAQ,CAACf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,IAAI,CAAC,EAAE;IACjE;IACA,MAAMC,aAAa,GAAG,cAAc,CAAAjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,IAAI,KAAI,UAAU,EAAE;IAC9D,oBAAOtB,OAAA,CAACH,QAAQ;MAACqB,EAAE,EAAEK,aAAc;MAACJ,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAChD;EAEA,OAAOf,QAAQ;AACjB,CAAC;AAACE,EAAA,CA/BIH,cAAc;EAAA,QAC2BH,OAAO;AAAA;AAAA0B,EAAA,GADhDvB,cAAc;AAiCpB,eAAeA,cAAc;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}